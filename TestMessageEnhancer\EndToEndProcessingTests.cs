using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Services;
using System.Text;

namespace TestMessageEnhancer;

/// <summary>
/// End-to-end processing tests that validate the complete HL7 message transformation workflow
/// </summary>
public class EndToEndProcessingTests : IDisposable
{
    private readonly ILogger<Hl7Processor> _logger;
    private readonly string _testDirectory;
    private readonly string _sampleFilePath;
    private readonly string _processedFilePath;
    private readonly string _expectedFilePath;

    public EndToEndProcessingTests()
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));
        _logger = loggerFactory.CreateLogger<Hl7Processor>();
        
        _testDirectory = Directory.GetCurrentDirectory();
        _sampleFilePath = Path.Combine(_testDirectory, "sample-hl7.hl7");
        _processedFilePath = Path.Combine(_testDirectory, "processed-sample-hl7.hl7");
        _expectedFilePath = Path.Combine(_testDirectory, "expected-hl7.hl7");
    }

    [Fact]
    public void ProcessSampleHL7Message_ShouldProduceExpectedOutput()
    {
        // Arrange
        VerifyTestFilesExist();
        
        // Clean up any existing processed file
        if (File.Exists(_processedFilePath))
        {
            File.Delete(_processedFilePath);
        }

        // Act
        var success = ProcessSampleFile();

        // Assert
        success.Should().BeTrue("Processing should complete successfully");
        File.Exists(_processedFilePath).Should().BeTrue("Processed file should be created");
        
        // Compare with expected output
        var comparisonResult = CompareProcessedWithExpected();
        comparisonResult.IsMatch.Should().BeTrue(comparisonResult.ErrorMessage);
    }

    [Fact]
    public void ProcessSampleHL7Message_WithOverwrite_ShouldReplaceExistingFile()
    {
        // Arrange
        VerifyTestFilesExist();
        
        // Create a dummy processed file first
        File.WriteAllText(_processedFilePath, "DUMMY CONTENT");
        var originalSize = new FileInfo(_processedFilePath).Length;

        // Act
        var success = ProcessSampleFile();

        // Assert
        success.Should().BeTrue("Processing should complete successfully");
        File.Exists(_processedFilePath).Should().BeTrue("Processed file should exist");
        
        var newSize = new FileInfo(_processedFilePath).Length;
        newSize.Should().NotBe(originalSize, "File should be overwritten with new content");
        
        // Verify content is correct
        var comparisonResult = CompareProcessedWithExpected();
        comparisonResult.IsMatch.Should().BeTrue(comparisonResult.ErrorMessage);
    }

    [Fact]
    public void ProcessedMessage_ShouldHaveCorrectTransformations()
    {
        // Arrange
        VerifyTestFilesExist();
        if (File.Exists(_processedFilePath))
        {
            File.Delete(_processedFilePath);
        }

        // Act
        var success = ProcessSampleFile();

        // Assert
        success.Should().BeTrue("Processing should complete successfully");
        
        var processedContent = File.ReadAllText(_processedFilePath);
        var processedLines = SplitHL7Content(processedContent);

        // Verify specific transformations
        VerifyMSHTransformation(processedLines);
        VerifyPIDTransformation(processedLines);
        VerifyROLSegmentCreation(processedLines);
        VerifyPD1Transformation(processedLines);
        VerifyOBXRemoval(processedLines);
    }

    private void VerifyTestFilesExist()
    {
        File.Exists(_sampleFilePath).Should().BeTrue($"Sample file should exist at {_sampleFilePath}");
        File.Exists(_expectedFilePath).Should().BeTrue($"Expected file should exist at {_expectedFilePath}");
    }

    private bool ProcessSampleFile()
    {
        try
        {
            // Create a temporary source directory and copy the sample file
            var tempSourceDir = Path.Combine(_testDirectory, "temp_source");
            var tempOutputDir = Path.Combine(_testDirectory, "temp_output");
            
            // Clean up temp directories
            if (Directory.Exists(tempSourceDir)) Directory.Delete(tempSourceDir, true);
            if (Directory.Exists(tempOutputDir)) Directory.Delete(tempOutputDir, true);
            
            Directory.CreateDirectory(tempSourceDir);
            
            // Copy sample file to temp source
            var tempSamplePath = Path.Combine(tempSourceDir, "sample-hl7.hl7");
            File.Copy(_sampleFilePath, tempSamplePath);

            // Process using Hl7Processor
            var processor = new Hl7Processor(_logger, tempSourceDir, tempOutputDir, false);
            processor.Run();

            // Copy the processed file to the expected location
            var tempProcessedPath = Path.Combine(tempOutputDir, "sample-hl7.hl7");
            if (File.Exists(tempProcessedPath))
            {
                File.Copy(tempProcessedPath, _processedFilePath, true);
            }

            // Clean up temp directories
            if (Directory.Exists(tempSourceDir)) Directory.Delete(tempSourceDir, true);
            if (Directory.Exists(tempOutputDir)) Directory.Delete(tempOutputDir, true);

            return processor.Statistics.FilesEnhanced > 0 && processor.Statistics.ErrorsEncountered == 0;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to process sample file: {ex.Message}", ex);
        }
    }

    private (bool IsMatch, string ErrorMessage) CompareProcessedWithExpected()
    {
        try
        {
            var processedContent = File.ReadAllText(_processedFilePath, Encoding.UTF8);
            var expectedContent = File.ReadAllText(_expectedFilePath, Encoding.UTF8);

            var processedLines = SplitHL7Content(processedContent);
            var expectedLines = SplitHL7Content(expectedContent);

            if (processedLines.Count != expectedLines.Count)
            {
                return (false, $"Line count mismatch. Processed: {processedLines.Count}, Expected: {expectedLines.Count}");
            }

            for (int i = 0; i < processedLines.Count; i++)
            {
                if (processedLines[i] != expectedLines[i])
                {
                    return (false, $"Line {i + 1} mismatch.\nProcessed: {processedLines[i]}\nExpected:  {expectedLines[i]}");
                }
            }

            return (true, string.Empty);
        }
        catch (Exception ex)
        {
            return (false, $"Error comparing files: {ex.Message}");
        }
    }

    private List<string> SplitHL7Content(string content)
    {
        // Handle different line endings and split into segments
        var normalized = content.Replace("\r\n", "\r").Replace("\n", "\r");
        return normalized.Split('\r', StringSplitOptions.RemoveEmptyEntries).ToList();
    }

    private void VerifyMSHTransformation(List<string> processedLines)
    {
        var mshLine = processedLines.FirstOrDefault(l => l.StartsWith("MSH"));
        mshLine.Should().NotBeNull("MSH segment should exist");
        
        var mshFields = mshLine!.Split('|');
        mshFields.Should().HaveCountGreaterThan(12, "MSH should have enough fields");
        mshFields[12].Should().Be("2.8", "MSH-12 (Version ID) should be updated to 2.8");
        mshFields[11].Should().Be("P", "MSH-11 (Processing ID) should be set to P");
    }

    private void VerifyPIDTransformation(List<string> processedLines)
    {
        var pidLine = processedLines.FirstOrDefault(l => l.StartsWith("PID"));
        pidLine.Should().NotBeNull("PID segment should exist");
        
        // Verify FULLREG mapping to PID-30 (should be "Y")
        var pidFields = pidLine!.Split('|');
        pidFields.Should().HaveCountGreaterThan(30, "PID should have enough fields for PID-30");
        pidFields[30].Should().Be("Y", "PID-30 should contain 'Y' from FULLREG mapping");
        
        // Verify HC EXP DATE is mapped to PID-3 component
        pidFields[3].Should().Contain("20240405", "PID-3 should contain HC EXP DATE value");
    }

    private void VerifyROLSegmentCreation(List<string> processedLines)
    {
        var rolLine = processedLines.FirstOrDefault(l => l.StartsWith("ROL"));
        rolLine.Should().NotBeNull("ROL segment should be created for FAMILY_PHYSICIAN");
        
        var rolFields = rolLine!.Split('|');
        rolFields[4].Should().Contain("10096519^Wally^Ahmed^Nourelfalah Mahmoud", 
            "ROL-4 should contain the family physician data");
    }

    private void VerifyPD1Transformation(List<string> processedLines)
    {
        var pd1Line = processedLines.FirstOrDefault(l => l.StartsWith("PD1"));
        pd1Line.Should().NotBeNull("PD1 segment should exist");
        
        var pd1Fields = pd1Line!.Split('|');
        pd1Fields.Should().HaveCountGreaterThan(3, "PD1 should have enough fields");
        pd1Fields[3].Should().Be("Rawdat Al Khail Health Center", 
            "PD1-3 should contain the primary organization name");
    }

    private void VerifyOBXRemoval(List<string> processedLines)
    {
        var obxLines = processedLines.Where(l => l.StartsWith("OBX")).ToList();
        obxLines.Should().BeEmpty("All OBX segments should be removed after processing");
    }

    [Fact]
    public void ProcessSampleHL7_SaveAsProcessedSampleHL7_ShouldMatchExpectedHL7()
    {
        // Arrange - Verify all required files exist
        File.Exists(_sampleFilePath).Should().BeTrue($"Sample HL7 file must exist at: {_sampleFilePath}");
        File.Exists(_expectedFilePath).Should().BeTrue($"Expected HL7 file must exist at: {_expectedFilePath}");

        // Clean up any existing processed file (overwrite scenario)
        if (File.Exists(_processedFilePath))
        {
            File.Delete(_processedFilePath);
        }

        // Act - Process the sample-hl7.hl7 message
        var processor = CreateProcessorForSingleFile();
        var success = ProcessSingleFileAndSaveAsProcessed();

        // Assert - Verify processing was successful
        success.Should().BeTrue("HL7 message processing should complete successfully");
        File.Exists(_processedFilePath).Should().BeTrue($"Processed file should be created at: {_processedFilePath}");

        // Assert - Compare processed-sample-hl7.hl7 with expected-hl7.hl7
        var filesAreIdentical = CompareFilesLineByLine(_processedFilePath, _expectedFilePath);
        filesAreIdentical.IsIdentical.Should().BeTrue(
            $"Processed file should be identical to expected file.\n{filesAreIdentical.DifferenceDetails}");
    }

    private Hl7Processor CreateProcessorForSingleFile()
    {
        return new Hl7Processor(_logger, _testDirectory, _testDirectory, false);
    }

    private bool ProcessSingleFileAndSaveAsProcessed()
    {
        try
        {
            // Read the sample HL7 file
            var sampleContent = File.ReadAllText(_sampleFilePath, Encoding.UTF8);

            // Create a minimal processor to handle the transformation
            var processor = CreateProcessorForSingleFile();

            // Process the file directly using the processor's internal methods
            // Since we need to access internal methods, we'll use the public interface
            var tempDir = Path.Combine(_testDirectory, "temp_processing");
            if (Directory.Exists(tempDir)) Directory.Delete(tempDir, true);
            Directory.CreateDirectory(tempDir);

            var tempInputFile = Path.Combine(tempDir, "input.hl7");
            File.WriteAllText(tempInputFile, sampleContent, Encoding.UTF8);

            // Process using the processor
            var processorForTemp = new Hl7Processor(_logger, tempDir, tempDir, false);
            var result = processorForTemp.ProcessFile(tempInputFile);

            if (result)
            {
                var tempOutputFile = Path.Combine(tempDir, "input.hl7");
                if (File.Exists(tempOutputFile))
                {
                    // Copy to the target processed file location
                    File.Copy(tempOutputFile, _processedFilePath, true);
                }
            }

            // Clean up temp directory
            if (Directory.Exists(tempDir)) Directory.Delete(tempDir, true);

            return result && File.Exists(_processedFilePath);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to process and save HL7 file: {ex.Message}", ex);
        }
    }

    private (bool IsIdentical, string DifferenceDetails) CompareFilesLineByLine(string file1Path, string file2Path)
    {
        try
        {
            var file1Content = File.ReadAllText(file1Path, Encoding.UTF8);
            var file2Content = File.ReadAllText(file2Path, Encoding.UTF8);

            var file1Lines = SplitHL7Content(file1Content);
            var file2Lines = SplitHL7Content(file2Content);

            if (file1Lines.Count != file2Lines.Count)
            {
                return (false, $"Line count differs: Processed={file1Lines.Count}, Expected={file2Lines.Count}");
            }

            var differences = new List<string>();
            for (int i = 0; i < file1Lines.Count; i++)
            {
                if (file1Lines[i] != file2Lines[i])
                {
                    differences.Add($"Line {i + 1}:");
                    differences.Add($"  Processed: {file1Lines[i]}");
                    differences.Add($"  Expected:  {file2Lines[i]}");
                }
            }

            if (differences.Any())
            {
                return (false, string.Join(Environment.NewLine, differences));
            }

            return (true, "Files are identical");
        }
        catch (Exception ex)
        {
            return (false, $"Error comparing files: {ex.Message}");
        }
    }

    public void Dispose()
    {
        // Clean up any temporary files created during testing
        if (File.Exists(_processedFilePath))
        {
            try
            {
                File.Delete(_processedFilePath);
            }
            catch
            {
                // Ignore cleanup errors
            }
        }
    }
}
