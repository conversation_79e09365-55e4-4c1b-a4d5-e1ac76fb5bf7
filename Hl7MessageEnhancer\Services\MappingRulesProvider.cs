using Hl7MessageEnhancer.Models;

namespace Hl7MessageEnhancer.Services;

/// <summary>
/// Provides hardcoded mapping rules for HL7 message enhancement
/// </summary>
public static class MappingRulesProvider
{
    /// <summary>
    /// Gets the default set of mapping rules for OBX segment transformations
    /// </summary>
    /// <returns>List of mapping rules</returns>
    public static List<MappingRule> GetDefaultMappingRules()
    {
        return new List<MappingRule>
        {
            new MappingRule
            {
                ObxField = "OBX-3.1",
                ObxValue = "QATAR_ID_EXP",
                TargetSegment = "",
                TargetField = "",
                RemoveOriginal = true,
                Description = "Remove Qatar ID expiration OBX (value already exists in PID)"
            },
            new MappingRule
            {
                ObxField = "OBX-3.1",
                ObxValue = "HC EXP DATE",
                TargetSegment = "PID",
                TargetField = "3.x.8",
                RemoveOriginal = true,
                Description = "Map health card expiration date from OBX to PID-3.7"
            },
            new MappingRule
            {
                ObxField = "OBX-3.1",
                ObxValue = "FAMILY_PHYSICIAN",
                TargetSegment = "ROL",
                TargetField = "4",
                RemoveOriginal = true,
                Description = "Map family physician from OBX to PID related ROL-4"
            },
            new MappingRule
            {
                ObxField = "OBX-3.1",
                ObxValue = "PRIM_ORG_NAME",
                TargetSegment = "PD1",
                TargetField = "3",
                RemoveOriginal = true,
                Description = "Map primary organization name from OBX to PD1-3 (Patient Primary Facility)"
            },
            new MappingRule
            {
                ObxField = "OBX-3.1",
                ObxValue = "FULLREG",
                TargetSegment = "PID",
                TargetField = "30",
                RemoveOriginal = true,
                Description = "Map full registration from OBX to PID-30 (replace No with Y)",
                ValueMapping = new Dictionary<string, string>
                {
                    { "*", "Y" }
                }
            }
        };
    }

    /// <summary>
    /// Gets mapping rules for a specific healthcare system or configuration
    /// </summary>
    /// <param name="configurationName">Name of the configuration</param>
    /// <returns>List of mapping rules for the specified configuration</returns>
    public static List<MappingRule> GetMappingRulesForConfiguration(string configurationName)
    {
        return configurationName.ToUpperInvariant() switch
        {
            "DEFAULT" or "PHCC" => GetDefaultMappingRules(),
            "MINIMAL" => GetMinimalMappingRules(),
            _ => GetDefaultMappingRules()
        };
    }

    /// <summary>
    /// Gets a minimal set of mapping rules for basic processing
    /// </summary>
    /// <returns>List of minimal mapping rules</returns>
    private static List<MappingRule> GetMinimalMappingRules()
    {
        return new List<MappingRule>
        {
            new MappingRule
            {
                ObxField = "OBX-3.1",
                ObxValue = "FULLREG",
                TargetSegment = "PID",
                TargetField = "30",
                RemoveOriginal = true,
                Description = "Map full registration from OBX to PID-30",
                ValueMapping = new Dictionary<string, string>
                {
                    { "*", "Y" }
                }
            }
        };
    }
}
