#!/usr/bin/env python3
"""
Test script to process the sample HL7 file and compare with expected output
"""

import os
import sys
import shutil
from pathlib import Path

# Import the HL7Processor from main.py
from main import HL7Processor

def process_sample_file():
    """Process the sample HL7 file"""
    
    # Create a temporary directory structure for testing
    temp_source = Path("temp_source")
    temp_output = Path("temp_output")
    
    # Clean up any existing temp directories
    if temp_source.exists():
        shutil.rmtree(temp_source)
    if temp_output.exists():
        shutil.rmtree(temp_output)
    
    try:
        # Create temp source directory and copy sample file
        temp_source.mkdir()
        shutil.copy("sample-hl7.hl7", temp_source / "sample-hl7.hl7")
        
        # Create processor and run
        processor = HL7Processor(str(temp_source), str(temp_output), verbose=True)
        processor.run()
        
        # Copy the processed file to the expected location
        processed_file = temp_output / "sample-hl7.hl7"
        if processed_file.exists():
            shutil.copy(processed_file, "processed-sample-hl7.hl7")
            print(f"✅ Processed file saved to: processed-sample-hl7.hl7")
        else:
            print("❌ No processed file was created")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error processing sample file: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up temp directories
        if temp_source.exists():
            shutil.rmtree(temp_source)
        if temp_output.exists():
            shutil.rmtree(temp_output)

def compare_files():
    """Compare processed file with expected file"""
    
    processed_file = Path("processed-sample-hl7.hl7")
    expected_file = Path("expected-hl7.hl7")
    
    if not processed_file.exists():
        print("❌ Processed file does not exist")
        return False
        
    if not expected_file.exists():
        print("❌ Expected file does not exist")
        return False
    
    try:
        with open(processed_file, 'r', encoding='utf-8') as f:
            processed_content = f.read().strip()
            
        with open(expected_file, 'r', encoding='utf-8') as f:
            expected_content = f.read().strip()
        
        print("\n📄 PROCESSED FILE CONTENT:")
        print("=" * 50)
        print(processed_content)
        print("=" * 50)
        
        print("\n📄 EXPECTED FILE CONTENT:")
        print("=" * 50)
        print(expected_content)
        print("=" * 50)
        
        if processed_content == expected_content:
            print("\n✅ FILES MATCH PERFECTLY!")
            return True
        else:
            print("\n❌ FILES DO NOT MATCH")
            
            # Show line-by-line comparison
            processed_lines = processed_content.split('\r')
            expected_lines = expected_content.split('\r')
            
            print("\n🔍 LINE-BY-LINE COMPARISON:")
            max_lines = max(len(processed_lines), len(expected_lines))
            
            for i in range(max_lines):
                proc_line = processed_lines[i] if i < len(processed_lines) else "[MISSING]"
                exp_line = expected_lines[i] if i < len(expected_lines) else "[MISSING]"
                
                if proc_line != exp_line:
                    print(f"Line {i+1} DIFFERS:")
                    print(f"  PROCESSED: {proc_line}")
                    print(f"  EXPECTED:  {exp_line}")
                else:
                    print(f"Line {i+1} MATCHES: {proc_line}")
            
            return False
            
    except Exception as e:
        print(f"❌ Error comparing files: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 TESTING HL7 SAMPLE PROCESSING")
    print("=" * 50)
    
    # Step 1: Process the sample file
    print("\n📝 Step 1: Processing sample file...")
    if not process_sample_file():
        print("❌ Failed to process sample file")
        sys.exit(1)
    
    # Step 2: Compare with expected output
    print("\n🔍 Step 2: Comparing with expected output...")
    if compare_files():
        print("\n🎉 SUCCESS: Processing is working correctly!")
        sys.exit(0)
    else:
        print("\n⚠️  NEEDS IMPROVEMENT: Output doesn't match expected result")
        sys.exit(1)

if __name__ == "__main__":
    main()
