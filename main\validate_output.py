#!/usr/bin/env python3
"""
Validation script to compare processed-sample-hl7.hl7 with expected-hl7.hl7
"""

import sys
from pathlib import Path
import difflib

def read_hl7_file(file_path):
    """Read HL7 file and return lines"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        # Split by both \r and \n to handle different line endings
        lines = content.replace('\r\n', '\n').replace('\r', '\n').split('\n')
        # Remove empty lines at the end
        while lines and not lines[-1].strip():
            lines.pop()
        return lines
    except Exception as e:
        print(f"ERROR: Failed to read {file_path}: {e}")
        return None

def compare_hl7_files(processed_file, expected_file):
    """Compare two HL7 files and report differences"""
    
    print(f"Comparing:")
    print(f"  Processed: {processed_file}")
    print(f"  Expected:  {expected_file}")
    print()
    
    # Read files
    processed_lines = read_hl7_file(processed_file)
    expected_lines = read_hl7_file(expected_file)
    
    if processed_lines is None or expected_lines is None:
        return False
    
    print(f"Processed file has {len(processed_lines)} lines")
    print(f"Expected file has {len(expected_lines)} lines")
    print()
    
    # Check if files are identical
    if processed_lines == expected_lines:
        print("✓ FILES MATCH EXACTLY!")
        return True
    
    print("✗ FILES DO NOT MATCH")
    print()
    
    # Show detailed differences
    print("=== DETAILED COMPARISON ===")
    
    # Line by line comparison
    max_lines = max(len(processed_lines), len(expected_lines))
    
    for i in range(max_lines):
        processed_line = processed_lines[i] if i < len(processed_lines) else "<MISSING>"
        expected_line = expected_lines[i] if i < len(expected_lines) else "<MISSING>"
        
        if processed_line != expected_line:
            print(f"Line {i+1}:")
            print(f"  Processed: {processed_line}")
            print(f"  Expected:  {expected_line}")
            print()
    
    # Show unified diff
    print("=== UNIFIED DIFF ===")
    diff = difflib.unified_diff(
        processed_lines,
        expected_lines,
        fromfile='processed-sample-hl7.hl7',
        tofile='expected-hl7.hl7',
        lineterm=''
    )
    
    diff_lines = list(diff)
    if diff_lines:
        for line in diff_lines:
            print(line)
    else:
        print("No differences found in unified diff")
    
    return False

def analyze_segments(processed_file, expected_file):
    """Analyze segment-level differences"""
    
    processed_lines = read_hl7_file(processed_file)
    expected_lines = read_hl7_file(expected_file)
    
    if processed_lines is None or expected_lines is None:
        return
    
    print("\n=== SEGMENT ANALYSIS ===")
    
    # Parse segments
    processed_segments = {}
    expected_segments = {}
    
    for line in processed_lines:
        if line.strip():
            segment_type = line[:3]
            if segment_type not in processed_segments:
                processed_segments[segment_type] = []
            processed_segments[segment_type].append(line)
    
    for line in expected_lines:
        if line.strip():
            segment_type = line[:3]
            if segment_type not in expected_segments:
                expected_segments[segment_type] = []
            expected_segments[segment_type].append(line)
    
    # Compare segments
    all_segment_types = set(processed_segments.keys()) | set(expected_segments.keys())
    
    for segment_type in sorted(all_segment_types):
        processed_count = len(processed_segments.get(segment_type, []))
        expected_count = len(expected_segments.get(segment_type, []))
        
        print(f"{segment_type}: Processed={processed_count}, Expected={expected_count}")
        
        if processed_count != expected_count:
            print(f"  ✗ Count mismatch for {segment_type}")
        elif processed_count > 0:
            # Compare content
            processed_segs = processed_segments.get(segment_type, [])
            expected_segs = expected_segments.get(segment_type, [])
            
            for i in range(processed_count):
                if processed_segs[i] != expected_segs[i]:
                    print(f"  ✗ Content differs for {segment_type}[{i}]")
                    break
            else:
                print(f"  ✓ {segment_type} matches")

def main():
    """Main validation function"""
    
    processed_file = Path("processed-sample-hl7.hl7")
    expected_file = Path("expected-hl7.hl7")
    
    if not processed_file.exists():
        print(f"ERROR: Processed file not found: {processed_file}")
        return False
    
    if not expected_file.exists():
        print(f"ERROR: Expected file not found: {expected_file}")
        return False
    
    # Compare files
    match = compare_hl7_files(processed_file, expected_file)
    
    # Analyze segments
    analyze_segments(processed_file, expected_file)
    
    return match

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
