using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Services;
using System.Text;

namespace TestMessageEnhancer;

/// <summary>
/// Simple end-to-end test that processes sample-hl7.hl7 and compares with expected-hl7.hl7
/// </summary>
public class SimpleEndToEndTest : IDisposable
{
    private readonly string _testDirectory;
    private readonly string _sampleFilePath;
    private readonly string _processedFilePath;
    private readonly string _expectedFilePath;

    public SimpleEndToEndTest()
    {
        _testDirectory = Directory.GetCurrentDirectory();
        _sampleFilePath = Path.Combine(_testDirectory, "sample-hl7.hl7");
        _processedFilePath = Path.Combine(_testDirectory, "processed-sample-hl7.hl7");
        _expectedFilePath = Path.Combine(_testDirectory, "expected-hl7.hl7");
    }

    [Fact]
    public void ProcessSampleHL7_ShouldCreateProcessedFile_AndMatchExpected()
    {
        // Arrange - Verify test files exist
        File.Exists(_sampleFilePath).Should().BeTrue($"Sample file must exist: {_sampleFilePath}");
        File.Exists(_expectedFilePath).Should().BeTrue($"Expected file must exist: {_expectedFilePath}");

        // Clean up any existing processed file
        if (File.Exists(_processedFilePath))
        {
            File.Delete(_processedFilePath);
        }

        // Act - Process the sample file
        var success = ProcessSampleFileToProcessedFile();

        // Assert - Verify processing succeeded
        success.Should().BeTrue("HL7 processing should succeed");
        File.Exists(_processedFilePath).Should().BeTrue($"Processed file should be created: {_processedFilePath}");

        // Assert - Compare files
        var comparison = CompareFiles(_processedFilePath, _expectedFilePath);

        // Debug output to see what's different
        if (!comparison.AreIdentical)
        {
            var processedContent = File.ReadAllText(_processedFilePath);
            var expectedContent = File.ReadAllText(_expectedFilePath);
            Console.WriteLine($"Processed content length: {processedContent.Length}");
            Console.WriteLine($"Expected content length: {expectedContent.Length}");
            Console.WriteLine($"Processed content: {processedContent}");
            Console.WriteLine($"Expected content: {expectedContent}");

            // Debug MSH field specifically
            var processedLines = processedContent.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            var expectedLines = expectedContent.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            var processedMsh = processedLines.FirstOrDefault(l => l.StartsWith("MSH"));
            var expectedMsh = expectedLines.FirstOrDefault(l => l.StartsWith("MSH"));

            Console.WriteLine($"Processed MSH: {processedMsh}");
            Console.WriteLine($"Expected MSH: {expectedMsh}");

            if (processedMsh != null)
            {
                var mshFields = processedMsh.Split('|');
                Console.WriteLine($"MSH field count: {mshFields.Length}");
                if (mshFields.Length > 12)
                {
                    Console.WriteLine($"MSH-12 (Version): '{mshFields[12]}'");
                }
                if (mshFields.Length > 11)
                {
                    Console.WriteLine($"MSH-11 (Processing ID): '{mshFields[11]}'");
                }
            }
        }

        comparison.AreIdentical.Should().BeTrue($"Files should be identical.\n{comparison.Differences}");
    }

    private bool ProcessSampleFileToProcessedFile()
    {
        try
        {
            // Create logger with debug output
            using var loggerFactory = LoggerFactory.Create(builder =>
                builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
            var logger = loggerFactory.CreateLogger<Hl7Processor>();

            // Read sample content
            var sampleContent = File.ReadAllText(_sampleFilePath, Encoding.UTF8);

            // Create temporary directories for processing
            var tempSourceDir = Path.Combine(_testDirectory, "temp_source");
            var tempOutputDir = Path.Combine(_testDirectory, "temp_output");

            if (Directory.Exists(tempSourceDir))
                Directory.Delete(tempSourceDir, true);
            if (Directory.Exists(tempOutputDir))
                Directory.Delete(tempOutputDir, true);

            Directory.CreateDirectory(tempSourceDir);

            try
            {
                // Write sample to temp source file
                var tempInputFile = Path.Combine(tempSourceDir, "sample.hl7");
                File.WriteAllText(tempInputFile, sampleContent, Encoding.UTF8);

                // Create processor and run it (this will process all files in source dir)
                var processor = new Hl7Processor(logger, tempSourceDir, tempOutputDir, false);
                processor.Run();

                // Check if processing was successful
                var success = processor.Statistics.FilesEnhanced > 0 && processor.Statistics.ErrorsEncountered == 0;

                if (success)
                {
                    // Copy processed file to target location
                    var tempOutputFile = Path.Combine(tempOutputDir, "sample.hl7");
                    if (File.Exists(tempOutputFile))
                    {
                        var processedContent = File.ReadAllText(tempOutputFile, Encoding.UTF8);
                        File.WriteAllText(_processedFilePath, processedContent, Encoding.UTF8);
                    }
                }

                return success;
            }
            finally
            {
                // Clean up temp directories
                if (Directory.Exists(tempSourceDir))
                    Directory.Delete(tempSourceDir, true);
                if (Directory.Exists(tempOutputDir))
                    Directory.Delete(tempOutputDir, true);
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to process sample file: {ex.Message}", ex);
        }
    }

    private (bool AreIdentical, string Differences) CompareFiles(string file1, string file2)
    {
        try
        {
            var content1 = File.ReadAllText(file1, Encoding.UTF8);
            var content2 = File.ReadAllText(file2, Encoding.UTF8);

            // Normalize line endings and split into lines
            var lines1 = NormalizeAndSplit(content1);
            var lines2 = NormalizeAndSplit(content2);

            if (lines1.Count != lines2.Count)
            {
                return (false, $"Line count differs: {file1}={lines1.Count}, {file2}={lines2.Count}");
            }

            var differences = new List<string>();
            for (int i = 0; i < lines1.Count; i++)
            {
                if (lines1[i] != lines2[i])
                {
                    differences.Add($"Line {i + 1} differs:");
                    differences.Add($"  Processed: {lines1[i]}");
                    differences.Add($"  Expected:  {lines2[i]}");
                }
            }

            if (differences.Any())
            {
                return (false, string.Join(Environment.NewLine, differences));
            }

            return (true, "Files are identical");
        }
        catch (Exception ex)
        {
            return (false, $"Error comparing files: {ex.Message}");
        }
    }

    private List<string> NormalizeAndSplit(string content)
    {
        // Handle different line endings - HL7 uses \r as segment separator
        var normalized = content.Replace("\r\n", "\r").Replace("\n", "\r");
        return normalized.Split('\r', StringSplitOptions.RemoveEmptyEntries).ToList();
    }

    public void Dispose()
    {
        // Clean up processed file if it exists
        if (File.Exists(_processedFilePath))
        {
            try
            {
                File.Delete(_processedFilePath);
            }
            catch
            {
                // Ignore cleanup errors
            }
        }
    }
}
