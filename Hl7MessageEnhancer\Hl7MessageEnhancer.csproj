﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="NHapi.Base" Version="3.2.3" />
        <PackageReference Include="NHapi.Model.V28" Version="3.2.4" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.6" />
    </ItemGroup>

    <ItemGroup>
        <None Update="sample-hl7.hl7">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="expected-hl7.hl7">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>
