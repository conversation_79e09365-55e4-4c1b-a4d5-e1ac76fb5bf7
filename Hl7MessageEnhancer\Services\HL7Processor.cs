using System.Text;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NHapi.Base.Model;
using NHapi.Base.Parser;
using NHapi.Model.V28.Message;
using NHapi.Model.V28.Segment;
using Hl7MessageEnhancer.Exceptions;
using Hl7MessageEnhancer.Models;

namespace Hl7MessageEnhancer.Services;

/// <summary>
/// Main HL7 processing service for enhancing HL7 messages
/// </summary>
public class Hl7Processor
{
    private readonly ILogger<Hl7Processor> _logger;
    private readonly string _sourceDirectory;
    private readonly string _outputDirectory;
    private readonly string _quarantineDirectory;
    private readonly bool _verbose;
    private readonly string _configurationName;
    private readonly ProcessingStatistics _statistics;
    private readonly List<MappingRule> _mappingRules;
    private readonly PipeParser _parser;
    private readonly string _errorLogPath;

    /// <summary>
    /// Initializes a new instance of the HL7Processor class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="sourceDirectory">Source directory for raw HL7 files</param>
    /// <param name="outputDirectory">Output directory for enhanced files</param>
    /// <param name="verbose">Enable verbose logging</param>
    /// <param name="configurationName">Name of the mapping rules configuration to use</param>
    public Hl7Processor(ILogger<Hl7Processor> logger, string sourceDirectory, string outputDirectory, bool verbose = false, string configurationName = "DEFAULT")
    {
        _logger = logger;
        _sourceDirectory = sourceDirectory;
        _outputDirectory = outputDirectory;
        _quarantineDirectory = "quarantine";
        _verbose = verbose;
        _configurationName = configurationName;
        _statistics = new ProcessingStatistics();
        _mappingRules = new List<MappingRule>();
        _parser = new PipeParser();
        _errorLogPath = "error_details.log";

        LoadConfiguration();
        CreateDirectories();
    }

    /// <summary>
    /// Gets the current processing statistics
    /// </summary>
    public ProcessingStatistics Statistics => _statistics;

    /// <summary>
    /// Load mapping rules and configuration
    /// </summary>
    private void LoadConfiguration()
    {
        try
        {
            // Load hardcoded mapping rules based on configuration
            var rules = MappingRulesProvider.GetMappingRulesForConfiguration(_configurationName);
            _mappingRules.AddRange(rules);
            _logger.LogInformation("Loaded {Count} hardcoded mapping rules for configuration '{Configuration}'", _mappingRules.Count, _configurationName);
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to load configuration: {ex.Message}", "CONFIG_ERROR", ex);
        }
    }

    /// <summary>
    /// Create necessary directories and clear output directory if it exists
    /// </summary>
    private void CreateDirectories()
    {
        try
        {
            // Clear output directory if it exists
            if (Directory.Exists(_outputDirectory))
            {
                Directory.Delete(_outputDirectory, true);
                _logger.LogInformation("Cleared existing output directory: {OutputDirectory}", _outputDirectory);
            }

            // Create directories
            Directory.CreateDirectory(_outputDirectory);
            Directory.CreateDirectory(_quarantineDirectory);
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to create directories: {ex.Message}", "DIRECTORY_ERROR", ex);
        }
    }

    /// <summary>
    /// Find all HL7 files in the source directory recursively
    /// </summary>
    /// <returns>List of HL7 file paths</returns>
    private List<string> FindHl7Files()
    {
        try
        {
            return Directory.GetFiles(_sourceDirectory, "*.hl7", SearchOption.AllDirectories).ToList();
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to scan directory {_sourceDirectory}: {ex.Message}", "DIRECTORY_ERROR", ex);
        }
    }

    /// <summary>
    /// Parse HL7 file into message object
    /// </summary>
    /// <param name="filePath">Path to the HL7 file</param>
    /// <returns>Parsed HL7 message</returns>
    private IMessage ParseHl7Message(string filePath)
    {
        try
        {
            var content = File.ReadAllText(filePath, Encoding.UTF8).Trim();

            if (string.IsNullOrEmpty(content))
            {
                throw new Hl7ProcessingException("Empty file", "HL7_PARSING_FAILURE", filePath);
            }

            // Preprocess HL7 content: ensure proper segment separators
            // NHapi expects \r as segment separator
            if (content.Contains('\n') && !content.Contains('\r'))
            {
                content = content.Replace('\n', '\r');
            }
            else if (!content.Contains('\n') && !content.Contains('\r'))
            {
                // Split by segment identifiers and rejoin with carriage returns
                var segmentPattern = @"(?=MSH|EVN|PID|PV1|OBX|OBR|NTE|AL1|DG1|PR1|GT1|IN1|IN2|IN3|ACC|UB1|UB2|ZQA|ZFM)";
                content = Regex.Replace(content, segmentPattern, "\r").Trim();
            }

            // Parse HL7 message
            var message = _parser.Parse(content);

            // Validate MSH segment exists
            if (message.GetStructure("MSH") == null)
            {
                throw new Hl7ProcessingException("MSH segment not found", "HL7_PARSING_FAILURE", filePath);
            }

            return message;
        }
        catch (Hl7ProcessingException)
        {
            throw;
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to parse HL7 message: {ex.Message}", "HL7_PARSING_FAILURE", ex, filePath);
        }
    }

    /// <summary>
    /// Apply standard HL7 enhancements to the message
    /// </summary>
    /// <param name="message">HL7 message to enhance</param>
    /// <returns>Enhanced HL7 message</returns>
    private IMessage ApplyHl7Enhancements(IMessage message)
    {
        try
        {
            // Get MSH segment using generic approach
            var mshSegment = (ISegment)message.GetStructure("MSH");

            // Update version to 2.8 (MSH-12)
            SetFieldValue(mshSegment, "12", "2.8");

            // Set processing ID to Production (MSH-11)
            SetFieldValue(mshSegment, "11", "P");

            // Apply OBX mapping rules
            ApplyObxMapping(message);

            // Ensure required segments exist for ADT messages
            EnsureRequiredSegments(message);

            return message;
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to apply enhancements: {ex.Message}", "PROCESSING_ERROR", ex);
        }
    }

    /// <summary>
    /// Apply OBX segment mapping rules
    /// </summary>
    /// <param name="message">HL7 message to process</param>
    private void ApplyObxMapping(IMessage message)
    {
        try
        {
            var obxSegments = new List<(int index, ISegment segment)>();
            var segmentsToRemove = new List<ISegment>();

            // Collect all OBX segments
            var structures = message.GetAll("OBX");
            for (int i = 0; i < structures.Length; i++)
            {
                if (structures[i] is ISegment segment)
                {
                    obxSegments.Add((i, segment));
                }
            }

            // Apply mapping rules
            foreach (var rule in _mappingRules)
            {
                foreach (var (_, obxSegment) in obxSegments)
                {
                    if (!ProcessObxSegment(message, obxSegment, rule))
                        continue;
                    if (!rule.RemoveOriginal)
                        continue;
                    segmentsToRemove.Add(obxSegment);
                }
            }

            // Remove OBX segments marked for removal
            foreach (var segment in segmentsToRemove)
            {
                // Note: NHapi doesn't have a direct remove method, so we'll need to work around this
                _logger.LogDebug("Marked OBX segment for removal: {Segment}", segment);
            }
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to apply OBX mapping: {ex.Message}", "MAPPING_RULE_ERROR", ex);
        }
    }

    /// <summary>
    /// Process a single OBX segment against a mapping rule
    /// </summary>
    /// <param name="message">HL7 message</param>
    /// <param name="obxSegment">OBX segment to process</param>
    /// <param name="rule">Mapping rule to apply</param>
    /// <returns>True if the rule was applied</returns>
    private bool ProcessObxSegment(IMessage message, ISegment obxSegment, MappingRule rule)
    {
        try
        {
            // Extract OBX-3.1 (first component of observation identifier) using generic approach
            var obx3Field = obxSegment.GetField(3, 0);
            var obxFieldValue = obx3Field?.ToString()?.Split('^')[0] ?? string.Empty;

            if (rule.ObxValue != obxFieldValue)
                return false;
            // Get the value to map (OBX-5)
            var obx5Field = obxSegment.GetField(5, 0);
            var valueToMap = obx5Field?.ToString()?.Trim() ?? string.Empty;

            // Apply value mapping if specified
            if (rule.ValueMapping != null)
            {
                // Check for a wildcard mapping first
                if (rule.ValueMapping.TryGetValue("*", out var wildcardValue))
                {
                    valueToMap = wildcardValue;
                    _logger.LogDebug("Applied wildcard value mapping: {ObxValue} -> '{ValueToMap}'", rule.ObxValue, valueToMap);
                }
                // Otherwise, check for a specific mapping
                else if (rule.ValueMapping.TryGetValue(valueToMap, out var mappedValue))
                {
                    var originalValue = valueToMap;
                    valueToMap = mappedValue;
                    _logger.LogDebug("Applied value mapping: {OriginalValue} -> '{ValueToMap}'", originalValue, valueToMap);
                }
            }

            // Only proceed with mapping if value is not empty
            if (!string.IsNullOrWhiteSpace(valueToMap))
            {
                MapValueToTargetSegment(message, rule, valueToMap);
                return true;
            }
            _logger.LogDebug("Skipping mapping for {ObxValue} - empty or whitespace-only value: '{ValueToMap}'", rule.ObxValue, valueToMap);

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing OBX segment for rule {ObxValue}", rule.ObxValue);
            return false;
        }
    }

    /// <summary>
    /// Map a value to the target segment and field
    /// </summary>
    /// <param name="message">HL7 message</param>
    /// <param name="rule">Mapping rule</param>
    /// <param name="value">Value to map</param>
    private void MapValueToTargetSegment(IMessage message, MappingRule rule, string value)
    {
        try
        {
            // Find or create target segment
            ISegment? targetSegment = null;

            try
            {
                targetSegment = (ISegment?)message.GetStructure(rule.TargetSegment);
            }
            catch
            {
                // Segment doesn't exist
            }

            // Create segment if it doesn't exist
            targetSegment ??= CreateTargetSegment(message, rule.TargetSegment);

            if (targetSegment != null)
            {
                SetFieldValue(targetSegment, rule.TargetField, value);
                _logger.LogDebug("Mapped {ObxValue} = '{Value}' to {TargetSegment}-{TargetField}",
                    rule.ObxValue, value, rule.TargetSegment, rule.TargetField);
            }
            else
            {
                _logger.LogWarning("Could not create or find target segment {TargetSegment} for {ObxValue}",
                    rule.TargetSegment, rule.ObxValue);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error mapping value to target segment {TargetSegment}", rule.TargetSegment);
        }
    }

    /// <summary>
    /// Create a target segment if it doesn't exist
    /// </summary>
    /// <param name="message">HL7 message</param>
    /// <param name="segmentName">Name of the segment to create</param>
    /// <returns>Created segment or null if not supported</returns>
    private ISegment? CreateTargetSegment(IMessage message, string segmentName)
    {
        // For now, we'll log that segment creation is needed
        // Full implementation would require more complex NHapi manipulation
        _logger.LogDebug("Target segment {SegmentName} creation needed", segmentName);
        return null;
    }

    /// <summary>
    /// Set a field value in a segment
    /// </summary>
    /// <param name="segment">Target segment</param>
    /// <param name="fieldPath">Field path (e.g., "3.x.8", "4", "30")</param>
    /// <param name="value">Value to set</param>
    private void SetFieldValue(ISegment segment, string fieldPath, string value)
    {
        try
        {
            var fieldParts = fieldPath.Split('.');
            var fieldNumber = int.Parse(fieldParts[0]);

            // Use reflection to set the field value directly
            var segmentType = segment.GetType();
            var fieldProperty = segmentType.GetProperty($"Field{fieldNumber}");

            if (fieldProperty != null)
            {
                var field = fieldProperty.GetValue(segment);
                if (field != null)
                {
                    // Try to set the value using the field's Value property or Parse method
                    var fieldType = field.GetType();
                    var valueProperty = fieldType.GetProperty("Value");
                    if (valueProperty != null && valueProperty.CanWrite)
                    {
                        valueProperty.SetValue(field, value);
                        _logger.LogDebug("Set field {FieldPath} to '{Value}' in segment {SegmentName}",
                            fieldPath, value, segment.GetStructureName());
                        return;
                    }
                }
            }

            // Fallback: try to modify the segment's encoded string representation
            _logger.LogDebug("Using fallback method for field {FieldPath} to '{Value}' in segment {SegmentName}",
                fieldPath, value, segment.GetStructureName());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set field {FieldPath} to '{Value}' in segment {SegmentName}",
                fieldPath, value, segment.GetStructureName());
        }
    }

    /// <summary>
    /// Ensure required segments exist for specific message types
    /// </summary>
    /// <param name="message">HL7 message to process</param>
    private void EnsureRequiredSegments(IMessage message)
    {
        try
        {
            // Get MSH segment to determine message type using generic approach
            var mshSegment = (ISegment)message.GetStructure("MSH");
            var messageTypeField = mshSegment.GetField(9, 0);
            var messageType = messageTypeField?.ToString()?.Split('^')[0] ?? string.Empty;

            _logger.LogDebug("Message type detected: {MessageType}", messageType);

            // For ADT messages, ensure EVN segment exists
            if (messageType != "ADT")
                return;
            try
            {
                message.GetStructure("EVN");
                _logger.LogDebug("EVN segment already exists");
            }
            catch
            {
                _logger.LogDebug("EVN segment missing, would create new one");
                // TODO: Implement EVN segment creation
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to ensure required segments");
        }
    }

    /// <summary>
    /// Save enhanced message preserving directory structure
    /// </summary>
    /// <param name="message">Enhanced HL7 message</param>
    /// <param name="originalPath">Original file path</param>
    private void SaveEnhancedMessage(IMessage message, string originalPath)
    {
        try
        {
            // Calculate relative path from source directory
            var relativePath = Path.GetRelativePath(_sourceDirectory, originalPath);
            var outputPath = Path.Combine(_outputDirectory, relativePath);

            // Create parent directories
            var outputDir = Path.GetDirectoryName(outputPath);
            if (!string.IsNullOrEmpty(outputDir))
            {
                Directory.CreateDirectory(outputDir);
            }

            // Convert message back to string with proper HL7 formatting
            var enhancedContent = _parser.Encode(message);

            // Write to file
            File.WriteAllText(outputPath, enhancedContent, Encoding.UTF8);

            _logger.LogInformation("Enhanced message saved: {OutputPath}", outputPath);
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to save enhanced message: {ex.Message}", "FILE_WRITE_ERROR", ex, originalPath);
        }
    }

    /// <summary>
    /// Move problematic file to quarantine with error details
    /// </summary>
    /// <param name="filePath">Path to the problematic file</param>
    /// <param name="error">The processing error</param>
    private void QuarantineFile(string filePath, Hl7ProcessingException error)
    {
        try
        {
            // Calculate relative path
            var relativePath = Path.GetRelativePath(_sourceDirectory, filePath);
            var quarantinePath = Path.Combine(_quarantineDirectory, relativePath);

            // Create parent directories
            var quarantineDir = Path.GetDirectoryName(quarantinePath);
            if (!string.IsNullOrEmpty(quarantineDir))
            {
                Directory.CreateDirectory(quarantineDir);
            }

            // Copy file to quarantine
            File.Copy(filePath, quarantinePath, true);

            // Create error details file
            var errorFile = Path.ChangeExtension(quarantinePath, ".error.json");
            var errorDetails = new
            {
                original_path = filePath,
                error_code = error.ErrorCode,
                error_message = error.Message,
                timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            };

            File.WriteAllText(errorFile, JsonConvert.SerializeObject(errorDetails, Formatting.Indented));

            _statistics.FilesQuarantined++;
            _logger.LogWarning("File quarantined: {QuarantinePath}", quarantinePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to quarantine file {FilePath}", filePath);
        }
    }

    /// <summary>
    /// Log error in machine-readable JSON format
    /// </summary>
    /// <param name="error">The processing error</param>
    /// <param name="context">Additional context information</param>
    private void LogError(Hl7ProcessingException error, Dictionary<string, object>? context = null)
    {
        var errorEntry = new
        {
            timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            level = "error",
            message = error.Message,
            filePath = error.FilePath,
            errorCode = error.ErrorCode,
            errorContext = context ?? new Dictionary<string, object>(),
            service = "hl7-enhancer"
        };

        var json = JsonConvert.SerializeObject(errorEntry);
        File.AppendAllText(_errorLogPath, json + Environment.NewLine);
    }

    /// <summary>
    /// Process a single HL7 file
    /// </summary>
    /// <param name="filePath">Path to the HL7 file</param>
    /// <returns>True if processing was successful</returns>
    public bool ProcessFile(string filePath)
    {
        try
        {
            _logger.LogInformation("Processing: {FilePath}", filePath);

            // Parse HL7 message
            var message = ParseHl7Message(filePath);

            // Apply enhancements
            var enhancedMessage = ApplyHl7Enhancements(message);

            // Save enhanced message
            SaveEnhancedMessage(enhancedMessage, filePath);

            _statistics.FilesEnhanced++;
            return true;
        }
        catch (Hl7ProcessingException ex)
        {
            _statistics.ErrorsEncountered++;

            // Log error
            LogError(ex, new Dictionary<string, object> { { "stack", ex.StackTrace ?? string.Empty } });
            _logger.LogError(ex, "Error processing {FilePath}: {Message}", filePath, ex.Message);

            // Quarantine file for certain error types
            if (ex.ErrorCode is "HL7_PARSING_FAILURE" or "PROCESSING_ERROR")
            {
                QuarantineFile(filePath, ex);
            }

            return false;
        }
        catch (Exception ex)
        {
            // Unexpected error
            var error = new Hl7ProcessingException($"Unexpected error: {ex.Message}", "PROCESSING_ERROR", ex, filePath);
            _statistics.ErrorsEncountered++;
            LogError(error, new Dictionary<string, object> { { "stack", ex.StackTrace ?? string.Empty } });
            _logger.LogError(ex, "Unexpected error processing {FilePath}", filePath);
            return false;
        }
    }

    /// <summary>
    /// Main processing loop
    /// </summary>
    public void Run()
    {
        var startTime = DateTime.Now;

        _logger.LogInformation("Starting HL7 Message Enhancement Engine");

        try
        {
            // Find all HL7 files
            var hl7Files = FindHl7Files();

            if (hl7Files.Count == 0)
            {
                _logger.LogWarning("No HL7 files found in {SourceDirectory}", _sourceDirectory);
                return;
            }

            _logger.LogInformation("Found {Count} HL7 files to process", hl7Files.Count);

            // Process each file
            foreach (var filePath in hl7Files)
            {
                _statistics.FilesProcessed++;
                var success = ProcessFile(filePath);
                if (_verbose && success)
                {
                    _logger.LogDebug("Successfully processed: {FilePath}", filePath);
                }
            }

            // Final statistics
            var elapsedTime = DateTime.Now - startTime;

            _logger.LogInformation("\nPROCESSING COMPLETE");
            _logger.LogInformation("Files processed successfully: {FilesProcessed}", _statistics.FilesProcessed);
            _logger.LogInformation("Files enhanced: {FilesEnhanced}", _statistics.FilesEnhanced);
            _logger.LogInformation("Errors encountered: {ErrorsEncountered}", _statistics.ErrorsEncountered);
            _logger.LogInformation("Files quarantined: {FilesQuarantined}", _statistics.FilesQuarantined);
            _logger.LogInformation("Processing time: {ElapsedTime:F2} seconds", elapsedTime.TotalSeconds);

            if (_statistics.ErrorsEncountered > 0)
            {
                _logger.LogInformation("Check error_details.log for detailed error analysis");
            }
        }
        catch (Hl7ProcessingException ex)
        {
            _logger.LogError(ex, "Processing failed: {Message}", ex.Message);
            LogError(ex);
            throw;
        }
        catch (Exception ex)
        {
            var error = new Hl7ProcessingException($"Unexpected system error: {ex.Message}", "PROCESSING_ERROR", ex, null);
            _logger.LogError(ex, "System error");
            LogError(error, new Dictionary<string, object> { { "stack", ex.StackTrace ?? string.Empty } });
            throw error;
        }
    }
}
