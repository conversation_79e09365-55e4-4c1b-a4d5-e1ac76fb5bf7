﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="coverlet.collector" Version="6.0.2"/>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0"/>
        <PackageReference Include="xunit" Version="2.9.2"/>
        <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2"/>
        <PackageReference Include="FluentAssertions" Version="6.12.2"/>
        <PackageReference Include="NHapi.Base" Version="3.2.3" />
        <PackageReference Include="NHapi.Model.V28" Version="3.2.4" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.6" />
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
        <Using Include="FluentAssertions"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Hl7MessageEnhancer\Hl7MessageEnhancer.csproj" />
    </ItemGroup>

</Project>
