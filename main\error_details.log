{"timestamp": "2025-07-07T10:47:07.675202Z", "level": "error", "message": "Failed to apply enhancements: Failed to apply OBX mapping: Segment.__init__() got multiple values for argument 'separator'", "filePath": null, "errorCode": "PROCESSING_ERROR", "errorContext": {"stack": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 286, in apply_obx_mapping\n    new_rol = hl7.Segment('ROL', ['', '', '', '', ''], separator=message.separator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: Segment.__init__() got multiple values for argument 'separator'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 229, in apply_hl7_enhancements\n    self.apply_obx_mapping(message)\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 375, in apply_obx_mapping\n    raise HL7ProcessingError(\nHL7ProcessingError: Failed to apply OBX mapping: Segment.__init__() got multiple values for argument 'separator'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 541, in process_file\n    enhanced_message = self.apply_hl7_enhancements(message)\n                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 237, in apply_hl7_enhancements\n    raise HL7ProcessingError(\nHL7ProcessingError: Failed to apply enhancements: Failed to apply OBX mapping: Segment.__init__() got multiple values for argument 'separator'\n"}, "service": "hl7-enhancer"}
{"timestamp": "2025-07-07T10:50:05.151217Z", "level": "error", "message": "Failed to apply enhancements: Failed to apply OBX mapping: ", "filePath": null, "errorCode": "PROCESSING_ERROR", "errorContext": {"stack": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 287, in apply_obx_mapping\n    new_rol = Segment(message.separator, ['ROL', '', '', '', ''])\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\site-packages\\hl7\\containers.py\", line 617, in __init__\n    assert not separator or separator == separators[1]\n                            ^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 229, in apply_hl7_enhancements\n    self.apply_obx_mapping(message)\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 377, in apply_obx_mapping\n    raise HL7ProcessingError(\nHL7ProcessingError: Failed to apply OBX mapping: \n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 543, in process_file\n    enhanced_message = self.apply_hl7_enhancements(message)\n                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 237, in apply_hl7_enhancements\n    raise HL7ProcessingError(\nHL7ProcessingError: Failed to apply enhancements: Failed to apply OBX mapping: \n"}, "service": "hl7-enhancer"}
{"timestamp": "2025-07-07T10:50:49.993859Z", "level": "error", "message": "Failed to apply enhancements: Failed to apply OBX mapping: ", "filePath": null, "errorCode": "PROCESSING_ERROR", "errorContext": {"stack": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 287, in apply_obx_mapping\n    new_rol = Segment(message.separator, ['ROL', '', '', '', ''])\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\site-packages\\hl7\\containers.py\", line 617, in __init__\n    assert not separator or separator == separators[1]\n                            ^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 229, in apply_hl7_enhancements\n    self.apply_obx_mapping(message)\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 377, in apply_obx_mapping\n    raise HL7ProcessingError(\nHL7ProcessingError: Failed to apply OBX mapping: \n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 543, in process_file\n    enhanced_message = self.apply_hl7_enhancements(message)\n                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 237, in apply_hl7_enhancements\n    raise HL7ProcessingError(\nHL7ProcessingError: Failed to apply enhancements: Failed to apply OBX mapping: \n"}, "service": "hl7-enhancer"}
{"timestamp": "2025-07-07T10:51:32.868915Z", "level": "error", "message": "Failed to apply enhancements: Failed to apply OBX mapping: invalid literal for int() with base 10: 'x'", "filePath": null, "errorCode": "PROCESSING_ERROR", "errorContext": {"stack": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 325, in apply_obx_mapping\n    component_num = int(field_parts[1]) - 1\n                    ^^^^^^^^^^^^^^^^^^^\nValueError: invalid literal for int() with base 10: 'x'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 229, in apply_hl7_enhancements\n    self.apply_obx_mapping(message)\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 377, in apply_obx_mapping\n    raise HL7ProcessingError(\nHL7ProcessingError: Failed to apply OBX mapping: invalid literal for int() with base 10: 'x'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 543, in process_file\n    enhanced_message = self.apply_hl7_enhancements(message)\n                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\OneDrive - easyguide\\Downloads\\main\\main.py\", line 237, in apply_hl7_enhancements\n    raise HL7ProcessingError(\nHL7ProcessingError: Failed to apply enhancements: Failed to apply OBX mapping: invalid literal for int() with base 10: 'x'\n"}, "service": "hl7-enhancer"}
