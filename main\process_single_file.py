#!/usr/bin/env python3
"""
Single file HL7 processor for sample-hl7.hl7 -> processed-sample-hl7.hl7
"""

import sys
import json
from pathlib import Path
import hl7
from main import HL7Processor

def process_single_file():
    """Process sample-hl7.hl7 and save to processed-sample-hl7.hl7"""
    
    input_file = Path("sample-hl7.hl7")
    output_file = Path("processed-sample-hl7.hl7")
    
    if not input_file.exists():
        print(f"ERROR: Input file not found: {input_file}")
        return False
    
    try:
        # Create a temporary processor instance
        processor = HL7Processor(".", ".", verbose=True)
        
        # Parse the input file
        message = processor.parse_hl7_message(input_file)
        
        # Apply enhancements
        enhanced_message = processor.apply_hl7_enhancements(message)

        # Debug: Show segments in enhanced message
        print("DEBUG: Segments in enhanced message:")
        for i, segment in enumerate(enhanced_message):
            segment_name = str(segment[0][0]) if len(segment) > 0 and len(segment[0]) > 0 else 'UNKNOWN'
            print(f"  {i}: {segment_name} - {str(segment)}")

        # Save to the specific output file
        with open(output_file, 'w', encoding='utf-8') as f:
            # Convert message back to string with proper HL7 formatting
            segments = []
            for segment in enhanced_message:
                segments.append(str(segment))
            
            # Join segments with carriage return (HL7 standard)
            hl7_content = '\r'.join(segments)
            f.write(hl7_content)
        
        print(f"SUCCESS: Processed {input_file} -> {output_file}")
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to process file: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = process_single_file()
    sys.exit(0 if success else 1)
